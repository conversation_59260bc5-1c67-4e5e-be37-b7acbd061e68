from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, HttpUrl
import json
from urllib.parse import urlparse
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright
from test import client
import asyncio
from typing import Optional

app = FastAPI(title="Spider Generator API", description="API to generate Scrapy spiders for job scraping")

class SpiderRequest(BaseModel):
    start_url: HttpUrl
    description: Optional[str] = "Generate a Scrapy spider for job scraping"

class SpiderResponse(BaseModel):
    spider_code: str
    job_links: list
    base_url: str
    success: bool
    message: str

def extract_job_links(start_url: str):
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto(str(start_url))
            parsed_url = urlparse(str(start_url))
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            page.wait_for_load_state("networkidle")
            html = page.content()
            soup = BeautifulSoup(html, "html.parser")
            for tag in soup.find_all(["script", "style", "path", "svg", "meta"]):
                tag.decompose()
            job_links = soup.find_all('a')
            prompt1 = (
                f"After reading the HTML, extract all job links from the following HTML. "
                f"If the link is not complete then add the base url {base_url} to the link. "
                "Write them in a Python list, do not include any introduction:\n" +
                "\n".join(str(tag) for tag in job_links)
            )
            chat_completion1 = client.chat.completions.create(
                messages=[{"role": "user", "content": prompt1}],
                model="openai/gpt-oss-120b",
            )
            step1 = chat_completion1.choices[0].message.content.strip()
            if step1.startswith("```"):
                step1 = step1.split("\n", 1)[1].rsplit("\n```", 1)[0]
            links = json.loads(step1)
            page.close()
            browser.close()
            return links, base_url
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error extracting job links: {str(e)}")

def generate_spider_code(start_url: str, base_url: str, first_job_url: str):
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto(first_job_url)
            page.wait_for_load_state("networkidle")
            html2 = page.content()
            soup2 = BeautifulSoup(html2, "html.parser")
            for tag2 in soup2.find_all([
                "script", "style", "path", "svg", "meta",
                'button', 'input', 'textarea', "fieldset",
                'form', 'head', 'footer', 'iframe'
            ]):
                tag2.decompose()
            prompt2 = (
                f"Generate a complete Scrapy spider for job scraping following this EXACT structure:\n\n"

                f"IMPORTS (copy exactly):\n"
                f"from jobsitescraper.log_manager import CustomLogger\n"
                f"import scrapy, re\n"
                f"import traceback, sys\n"
                f"from jobsitescraper.utils import env\n"
                f"from scrapy.exceptions import CloseSpider\n"
                f"from bs4 import BeautifulSoup\n"
                f"from datetime import datetime, timezone\n\n"

                f"CLASS NAME: Extract domain from {base_url} and create class named like 'domainSpider' (e.g., '4plcsComSpider')\n\n"

                f"CLASS VARIABLES (copy exactly):\n"
                f"name = '{base_url.replace('https://', '').replace('www.', '')}'\n"
                f"close_down = False\n"
                f"config = {{}}\n"
                f"i = 0\n"
                f"page_num = 1\n"
                f"total = 0\n"
                f"count = -1\n"
                f"isLive = env('PRODUCTION')\n\n"

                f"METHOD 1 - __init__(self, _config=None, **kwargs):\n"
                f"super().__init__(**kwargs)\n"
                f"if self.isLive == 'True':\n"
                f"    self.config = _config\n"
                f"else:\n"
                f"    self.config = self.get_config()\n\n"

                f"METHOD 2 - get_config(self) returns dict with EXACT keys:\n"
                f"SourceKey, BaseUrl, StartUrl, SourceCountry, LangCode, Upload, IsActive, Custom, MaxPagesToCrawl, MaxJobsToCrawl, RecentJobs, DeleteAllJobsOnStart\n"
                f"DeleteAllJobsOnStart should be True always\n"
                f"MaxPagesToCrawl should be 10 and MaxJobsToCrawl should be 500 always\n"
                f"Set BaseUrl = '{base_url}', StartUrl = '{start_url}' (or main jobs page), SourceCountry and LangCode based on website\n\n"

                f"METHOD 3 - start_requests(self):\n"
                f"Check config, log events with CustomLogger.LogEvent()\n"
                f"If RecentJobs=True: yield scrapy.Request to StartUrl with callback=self.parse_recent\n"
                f"Include try-except with CustomLogger.LogEvent() and traceback.format_exc()\n\n"

                f"METHOD 4 - parse_recent(self, response):\n"
                f"Decode response: website_text = response.body.decode('utf-8')\n"
                f"Create soup: BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')\n"
                f"Extract job links from HTML using soup.find_all()\n"
                f"For each link: yield scrapy.Request(link, callback=self.parse_job)\n"
                f"Include try-except with logging\n\n"

                f"METHOD 5 - parse_job(self, response) - MOST IMPORTANT:\n"
                f"Start with: if self.close_down: raise CloseSpider('took down by analyzer')\n"
                f"Extract using XPath/BeautifulSoup:\n"
                f"- jobTitle (strip whitespace, default empty string)\n"
                f"- jobLocation (clean \\xa0|\\xa0 characters), if you dont find the JobLocation then it will be Switzerland \n"
                f"- Parse description: find main content, remove tags ['a','img','svg','script','style', 'footer', 'textarea', 'button', 'path', 'head'], create cleanContent and rawContent\n"
                f"Create ad dict with ALL these fields:\n"
                f"JobTitle, JobLocation, CompanyLogoFileURL, CompanyName, SourceURL, SourceCountry, SourceKey, SourceLangCode, CrawlTimestamp, SourceUID, CleanContent, RawContent, PostedDate\n"
                f"RawContent is the HTML of the job post, it should have html tags always\n"
                f"Find the CompanyLogoFileURL from html, if you dont find it then create a default company logo url\n"
                f"Find the CompanyName from html\n"
                f"PostedDate and CrawlTimestamp should be datetime.now(timezone.utc).astimezone().isoformat() always\n"
                f"Extract JobContactEmails: re.findall('\\S+@\\S+', cleanContent.strip('\\n'))\n and make it string not list\n"
                f"Extract JobContactPhone: re.findall(r'[\\+\\(]?[1-9][0-9 \\-\\(\\)]{{8,}}[0-9]', cleanContent.strip('\\n').replace('\\u00a0', ' ')) and make it string not list\n"
                f"Upload logic: increment count, check MaxJobsToCrawl limit, yield ad\n"
                f"Include try-except with logging\n\n"

                f"METHOD 6 - close(self, reason):\n"
                f"Log 'Crawler Stopped, Total Jobs: {{self.count}}' with try-except\n\n"

                f"CRITICAL REQUIREMENTS:\n"
                f"- Use EXACT same structure as template\n"
                f"- All timestamps: datetime.now(timezone.utc).astimezone().isoformat()\n"
                f"- BeautifulSoup cleaning: remove ['a','img','svg','script','style'] with tag.decompose()\n"
                f"- Text cleaning: re.sub('\\s+', ' ', text)\n"
                f"- Same error handling pattern with CustomLogger.LogEvent()\n add this only(CustomLogger.LogEvent(self.config['SourceKey'], str(e)))\n"
                f"- Same variable names and method signatures\n\n"
                f"- Dont write comments in the code\n\n"
                f"- if pagination is there then add pagination logic carefully after checking the html\n\n"
                f"- if you detect heavy javascript in website content then only add scrapy playwright code in start_requests method and parse_recent method\n\n"

                f"Analyze this HTML to determine correct selectors for job data extraction:\n"
                + str(soup2)
            )
            chat_completion2 = client.chat.completions.create(
                messages=[{"role": "user", "content": prompt2}],
                model="openai/gpt-oss-120b"
            )
            spider_code = chat_completion2.choices[0].message.content
            page.close()
            browser.close()
            return spider_code
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating spider code: {str(e)}")

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Spider Generator API",
        "description": "Generate Scrapy spiders for job scraping from any job website",
        "endpoints": {
            "/generate-spider": "POST - Generate a complete Scrapy spider",
            "/health": "GET - Health check"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "API is running"}

@app.post("/generate-spider", response_model=SpiderResponse)
async def generate_spider(request: SpiderRequest):
    try:
        job_links, base_url = extract_job_links(request.start_url)
        if not job_links:
            raise HTTPException(status_code=400, detail="No job links found on the provided URL")
        first_job_url = job_links[0]
        spider_code = generate_spider_code(str(request.start_url), base_url, first_job_url)
        return SpiderResponse(
            spider_code=spider_code,
            job_links=job_links,
            base_url=base_url,
            success=True,
            message=f"Successfully generated spider for {base_url}. Found {len(job_links)} job links."
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

